import 'package:my_video/app_imports.dart';

class InitialSetupPage extends StatefulWidget {
  const InitialSetupPage({super.key});

  @override
  State<InitialSetupPage> createState() => _InitialSetupPageState();
}

class _InitialSetupPageState extends State<InitialSetupPage> {
  final MovieRepository _movieRepository = GetIt.instance<MovieRepository>();

  bool _isLoading = true;
  List<FilterLanguage> _availableLanguages = [];
  List<FilterGenre> _availableGenres = [];

  List<FilterLanguage> _selectedLanguages = [];
  List<FilterGenre> _selectedGenres = [];

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  Future<void> _initializeData() async {
    try {
      // First ensure we have an unregistered token for API calls
      await _ensureUnregisteredToken();

      // Load available filters from API
      final filterResponse = await _movieRepository.getFilters();

      setState(() {
        _availableLanguages = filterResponse.languages;
        _availableGenres = filterResponse.genres;
      });

      // Set default selections (English + Bollywood)
      final defaultFilters = UserPreferenceService.getDefaultPreferences();
      setState(() {
        _selectedLanguages = List.from(defaultFilters.selectedLanguages);
        _selectedGenres = List.from(defaultFilters.selectedGenres);
      });
    } catch (e) {
      AppHelper.logDebug('Error loading filters: $e');
      // Set default values if API fails
      setState(() {
        _availableLanguages = [
          FilterLanguage(langId: 1, language: 'English'),
          FilterLanguage(langId: 2, language: 'हिंदी'),
          FilterLanguage(langId: 3, language: 'ગુજરાતી'),
          FilterLanguage(langId: 5, language: 'தமிழ்'),
          FilterLanguage(langId: 7, language: 'മലയാളം'),
        ];
        _availableGenres = [
          FilterGenre(genreId: 3, genre: 'Bollywood'),
          FilterGenre(genreId: 4, genre: 'Hollywood'),
          FilterGenre(genreId: 2, genre: 'Tollywood'),
          FilterGenre(genreId: 1, genre: 'Mollywood'),
        ];

        // Set default selections
        _selectedLanguages = [FilterLanguage(langId: 1, language: 'English')];
        _selectedGenres = [FilterGenre(genreId: 3, genre: 'Bollywood')];
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _ensureUnregisteredToken() async {
    try {
      // Check if we already have a static token
      final existingToken = AppSharedPreference.getString('static_token');
      if (existingToken != null && existingToken.isNotEmpty) {
        return; // Already have token
      }

      // Get unregistered user token
      final authRepository = GetIt.instance<AuthenticationRepository>();
      final result = await authRepository.getUnregisteredUserToken();

      if (result['success'] == true) {
        AppHelper.logDebug('Successfully obtained unregistered token');
      } else {
        AppHelper.logDebug(
          'Failed to get unregistered token: ${result['message']}',
        );
      }
    } catch (e) {
      AppHelper.logDebug('Error getting unregistered token: $e');
    }
  }

  void _toggleLanguage(FilterLanguage language) {
    setState(() {
      if (_selectedLanguages.contains(language)) {
        _selectedLanguages.remove(language);
      } else {
        _selectedLanguages.add(language);
      }
    });
  }

  void _toggleGenre(FilterGenre genre) {
    setState(() {
      if (_selectedGenres.contains(genre)) {
        _selectedGenres.remove(genre);
      } else {
        _selectedGenres.add(genre);
      }
    });
  }

  Future<void> _completeSetup() async {
    try {
      // Ensure at least one language and genre is selected
      if (_selectedLanguages.isEmpty) {
        AppHelper.showToast(
          'Please select at least one language',
          isError: true,
        );
        return;
      }

      if (_selectedGenres.isEmpty) {
        AppHelper.showToast('Please select at least one genre', isError: true);
        return;
      }

      // Save preferences
      final selectedFilters = SelectedFilters(
        selectedLanguages: _selectedLanguages,
        selectedGenres: _selectedGenres,
        selectedCategories: [],
      );

      await UserPreferenceService.instance.saveSelectedFilters(selectedFilters);
      await UserPreferenceService.instance.setFirstLaunchCompleted();

      // Navigate to main app
      if (mounted) {
        context.go(AppRoutes.mainNavigation);
      }
    } catch (e) {
      AppHelper.logDebug('Error completing setup: $e');
      AppHelper.showToast('Failed to save preferences', isError: true);
    }
  }

  @override
  Widget build(BuildContext context) {
    // Initialize MySize for responsive design
    MySize.init(context);

    return AppScaffold(
      backgroundColor: AppColorConstants.backgroundColor,
      body: SafeArea(
        child: _isLoading ? _buildLoadingView() : _buildSetupView(),
      ),
    );
  }

  Widget _buildLoadingView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: AppColorConstants.primaryColor),
          Space.height(16),
          AppText(
            text: 'Setting up your preferences...',
            fontSize: 16,
            color: AppColorConstants.textSecondary,
          ),
        ],
      ),
    );
  }

  Widget _buildSetupView() {
    return Column(
      children: [
        // Header with gradient background
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                AppColorConstants.primaryColor.withOpacity(0.1),
                AppColorConstants.backgroundColor,
              ],
            ),
          ),
          padding: EdgeInsets.all(MySize.width(24)),
          child: Column(
            children: [
              Container(
                padding: EdgeInsets.all(MySize.width(16)),
                decoration: BoxDecoration(
                  color: AppColorConstants.primaryColor.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.movie_filter,
                  size: MySize.width(48),
                  color: AppColorConstants.primaryColor,
                ),
              ),
              Space.height(16),
              AppText(
                text: 'Welcome to My Video!',
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppColorConstants.colorWhite,
                textAlign: TextAlign.center,
              ),
              Space.height(8),
              AppText(
                text: 'Let\'s personalize your experience',
                fontSize: 16,
                color: AppColorConstants.textSecondary,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),

        // Content
        Expanded(
          child: SingleChildScrollView(
            padding: EdgeInsets.symmetric(horizontal: MySize.width(20)),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Space.height(24),

                // Languages Section
                _buildSectionCard(
                  title: 'Languages',
                  subtitle: 'Choose your preferred languages',
                  icon: Icons.language,
                  child: _buildLanguageChips(),
                ),

                Space.height(24),

                // Genres Section
                _buildSectionCard(
                  title: 'Genres',
                  subtitle: 'Pick your favorite movie genres',
                  icon: Icons.movie,
                  child: _buildGenreChips(),
                ),

                Space.height(100), // Bottom padding for button
              ],
            ),
          ),
        ),

        // Continue Button with gradient
        Container(
          padding: EdgeInsets.all(MySize.width(20)),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.bottomCenter,
              end: Alignment.topCenter,
              colors: [
                AppColorConstants.backgroundColor,
                AppColorConstants.backgroundColor.withOpacity(0.8),
              ],
            ),
          ),
          child: Column(
            children: [
              Container(
                width: double.infinity,
                height: MySize.height(52),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      AppColorConstants.primaryColor,
                      AppColorConstants.blue225BFF,
                    ],
                  ),
                  borderRadius: BorderRadius.circular(MySize.width(12)),
                  boxShadow: [
                    BoxShadow(
                      color: AppColorConstants.primaryColor.withOpacity(0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: ElevatedButton(
                  onPressed: _completeSetup,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.transparent,
                    shadowColor: Colors.transparent,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(MySize.width(12)),
                    ),
                  ),
                  child: AppText(
                    text: 'Continue',
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppColorConstants.colorWhite,
                  ),
                ),
              ),
              Space.height(12),
              AppText(
                text: 'You can change these preferences anytime in settings',
                fontSize: 12,
                color: AppColorConstants.textHint,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSectionCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Widget child,
  }) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(MySize.width(20)),
      decoration: BoxDecoration(
        color: AppColorConstants.cardColor,
        borderRadius: BorderRadius.circular(MySize.width(16)),
        border: Border.all(color: AppColorConstants.dividerColor, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(MySize.width(8)),
                decoration: BoxDecoration(
                  color: AppColorConstants.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(MySize.width(8)),
                ),
                child: Icon(
                  icon,
                  size: MySize.width(20),
                  color: AppColorConstants.primaryColor,
                ),
              ),
              Space.width(12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AppText(
                      text: title,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: AppColorConstants.colorWhite,
                    ),
                    Space.height(4),
                    AppText(
                      text: subtitle,
                      fontSize: 14,
                      color: AppColorConstants.textHint,
                    ),
                  ],
                ),
              ),
            ],
          ),
          Space.height(16),
          child,
        ],
      ),
    );
  }

  Widget _buildLanguageChips() {
    return Wrap(
      spacing: MySize.width(8),
      runSpacing: MySize.height(8),
      children: _availableLanguages.map((language) {
        final isSelected = _selectedLanguages.contains(language);
        return GestureDetector(
          onTap: () => _toggleLanguage(language),
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: MySize.width(16),
              vertical: MySize.height(10),
            ),
            decoration: BoxDecoration(
              gradient: isSelected
                  ? LinearGradient(
                      colors: [
                        AppColorConstants.primaryColor,
                        AppColorConstants.blue225BFF,
                      ],
                    )
                  : null,
              color: isSelected ? null : AppColorConstants.cardColor,
              borderRadius: BorderRadius.circular(MySize.width(20)),
              border: Border.all(
                color: isSelected
                    ? Colors.transparent
                    : AppColorConstants.colorGrey,
                width: 1,
              ),
              boxShadow: isSelected
                  ? [
                      BoxShadow(
                        color: AppColorConstants.primaryColor.withOpacity(0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ]
                  : null,
            ),
            child: AppText(
              text: language.language,
              fontSize: 14,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              color: isSelected
                  ? AppColorConstants.colorWhite
                  : AppColorConstants.colorGrey,
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildGenreChips() {
    return Wrap(
      spacing: MySize.width(8),
      runSpacing: MySize.height(8),
      children: _availableGenres.map((genre) {
        final isSelected = _selectedGenres.contains(genre);
        return GestureDetector(
          onTap: () => _toggleGenre(genre),
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: MySize.width(16),
              vertical: MySize.height(10),
            ),
            decoration: BoxDecoration(
              gradient: isSelected
                  ? LinearGradient(
                      colors: [
                        AppColorConstants.primaryColor,
                        AppColorConstants.blue225BFF,
                      ],
                    )
                  : null,
              color: isSelected ? null : AppColorConstants.cardColor,
              borderRadius: BorderRadius.circular(MySize.width(20)),
              border: Border.all(
                color: isSelected
                    ? Colors.transparent
                    : AppColorConstants.colorGrey,
                width: 1,
              ),
              boxShadow: isSelected
                  ? [
                      BoxShadow(
                        color: AppColorConstants.primaryColor.withOpacity(0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ]
                  : null,
            ),
            child: AppText(
              text: genre.genre,
              fontSize: 14,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              color: isSelected
                  ? AppColorConstants.colorWhite
                  : AppColorConstants.colorGrey,
            ),
          ),
        );
      }).toList(),
    );
  }
}
