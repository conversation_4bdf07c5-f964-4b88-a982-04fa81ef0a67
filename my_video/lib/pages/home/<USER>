import 'package:my_video/app_imports.dart';

class HomePageHelper {
  final HomePageState _state;
  final Logger _logger = Logger();

  // Loading state
  bool _isLoading = false;
  bool get isLoading => _isLoading;

  // Data variables
  List<MovieModel> _featuredMovies = [];
  List<CategoryWiseData> _categoryWiseData = [];
  Map<String, List<MovieModel>> _moviesByCategory = {};

  // Getters
  List<MovieModel> get featuredMovies => _featuredMovies;
  List<CategoryWiseData> get categories => _categoryWiseData;

  // API Status
  ApiStatus apiStatus = ApiStatus.initial;
  bool _hasLoadedData = false;

  HomePageHelper(this._state) {
    // Only load data if not already loaded (prevents reload on navigation back)
    if (!_hasLoadedData) {
      // Start loading data immediately without delay for better UX
      // Use Future.microtask to ensure controller is initialized
      Future.microtask(() => loadInitialData());
    }
  }

  Future<void> loadInitialData() async {
    _setLoading(true);
    apiStatus = ApiStatus.loading;
    _state.homeController.update();

    try {
      // Check if offline and has cached data
      final offlineManager = OfflineManager.instance;
      if (!offlineManager.isOnline && offlineManager.hasCachedData()) {
        await _loadCachedData();
        _setLoading(false);
        _state.homeController.update();
        return;
      }

      // Run token and data loading in parallel for better performance
      _logger.i('Loading data and ensuring auth token in parallel...');
      await Future.wait([_ensureAuthToken(), loadCategoryWiseData()]);

      apiStatus = ApiStatus.success;
      _hasLoadedData = true;
      _logger.i('Successfully loaded data from showcatwise API');
    } catch (e) {
      _logger.e('Error loading initial data: $e');
      ErrorHandler.handleError(e, context: 'Home Data Loading');
      apiStatus = ApiStatus.error;

      // Try to load cached data as fallback
      await _loadCachedData();
    } finally {
      _setLoading(false);
      _state.homeController.update();
    }
  }

  Future<void> _ensureAuthToken() async {
    try {
      // Check if we already have a token
      final userToken = AppSharedPreference.getUserToken();
      final staticToken = AppSharedPreference.getString('static_token');

      if (userToken != null || staticToken != null) {
        _logger.i('Authentication token already available');
        return;
      }

      // Get unregistered user token
      _logger.i('Getting unregistered user token...');
      final response = await RestHelper.post('/unregisteredusertoken');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['status'] == 1) {
          await AppSharedPreference.setString('static_token', data['token']);
          _logger.i('Unregistered token obtained successfully');
        } else {
          _logger.w('Failed to get unregistered token: ${data['message']}');
        }
      } else {
        _logger.w('Failed to get unregistered token: ${response.statusCode}');
      }
    } catch (e) {
      _logger.e('Error getting authentication token: $e');
    }
  }

  Future<void> loadCategoryWiseData({String? isMovie}) async {
    try {
      // Get user preferences for filtering
      final languageFilter = await UserPreferenceService.instance
          .getLanguageNamesForAPI();
      final genreFilter = await UserPreferenceService.instance
          .getGenreNamesForAPI();
      final categoryFilter = await UserPreferenceService.instance
          .getCategoryNamesForAPI();

      final response = await _state.homeController.getCategoryWiseMoviesFromAPI(
        languages: languageFilter,
        genres: genreFilter,
        categories: categoryFilter,
        isMovie: isMovie,
      );

      if (response.success && response.result.isNotEmpty) {
        _categoryWiseData = response.result;

        // Extract featured movies from the first category or create from all categories
        _featuredMovies = [];
        for (final categoryData in _categoryWiseData) {
          if (_featuredMovies.length < 5) {
            _featuredMovies.addAll(
              categoryData.data.take(5 - _featuredMovies.length),
            );
          }
        }

        // Populate movies by category map
        _moviesByCategory.clear();
        for (final categoryData in _categoryWiseData) {
          _moviesByCategory[categoryData.name] = categoryData.data;
        }

        _logger.i(
          'Loaded category wise data: ${_categoryWiseData.length} categories, ${_featuredMovies.length} featured movies',
        );
        _state.homeController.update();
      }
    } catch (e) {
      _logger.e('Error loading category wise data: $e');
      rethrow;
    }
  }

  Future<void> _loadCachedData() async {
    try {
      final offlineManager = OfflineManager.instance;
      _featuredMovies = offlineManager.getCachedFeaturedMovies();

      // For now, create empty category wise data from cached data
      _categoryWiseData = [];
      _moviesByCategory.clear();

      _logger.i(
        'Loaded cached data: ${_featuredMovies.length} featured movies',
      );
      apiStatus = ApiStatus.success;
      _hasLoadedData = true;
    } catch (e) {
      _logger.e('Error loading cached data: $e');
      apiStatus = ApiStatus.error;
    }
  }

  List<MovieModel> getMoviesByCategory(String categoryName) {
    return _moviesByCategory[categoryName] ?? [];
  }

  Future<void> refreshData() async {
    // Reset the loaded flag to force refresh when user explicitly pulls to refresh
    _hasLoadedData = false;
    await loadInitialData();
  }

  void playMovie(MovieModel movie, BuildContext context) {
    _logger.i('Playing movie: ${movie.title}');

    AdsManager.showInterstitialAd(
      onAdClosed: () {
        context.push(AppRoutes.moviePlayer, extra: movie);
      },
    );
  }

  void viewAllMovies(CategoryWiseData category) {
    _logger.i('View all movies for category: ${category.name}');
    AppHelper.showToast('View all ${category.name} movies');
  }

  // Load data for specific content type (Movies or Web Series)
  Future<void> loadDataForContentType(String isMovie) async {
    try {
      AppHelper.logDebug('Loading data for content type: $isMovie');

      // Clear existing data
      _categoryWiseData.clear();
      _featuredMovies.clear();
      _moviesByCategory.clear();

      // Set loading state
      _setLoading(true);

      // Load data with specific content type
      await loadCategoryWiseData(isMovie: isMovie);

      AppHelper.logDebug('Successfully loaded data for content type: $isMovie');
    } catch (e) {
      AppHelper.logDebug('Error loading data for content type: $e');
      ErrorHandler.handleError(e);
    } finally {
      _setLoading(false);
    }
  }

  // Refresh data with new filters
  Future<void> refreshWithFilters(SelectedFilters filters) async {
    try {
      AppHelper.logDebug(
        'Refreshing logged-in home page with filters: $filters',
      );

      // Save filters to preferences
      await UserPreferenceService.instance.saveSelectedFilters(filters);

      // Clear existing data
      _categoryWiseData.clear();
      _featuredMovies.clear();
      _moviesByCategory.clear();

      // Reload data with new filters and current content type
      final contentTabController = Get.find<ContentTabController>();
      final isMovie = contentTabController.currentApiValue;
      await loadCategoryWiseData(isMovie: isMovie);

      AppHelper.logDebug(
        'Successfully refreshed logged-in home page with filters',
      );
    } catch (e) {
      AppHelper.logDebug('Error refreshing with filters: $e');
      ErrorHandler.handleError(e);
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    _state.homeController.update();
  }
}
