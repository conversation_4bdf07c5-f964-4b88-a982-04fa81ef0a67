class AppRoutes {
  static const String splash = '/';
  static const String initialSetup = '/initial-setup';
  static const String login = '/login';
  static const String signup = '/signup';
  static const String forgotPassword = '/forgot-password';
  static const String home = '/home';
  static const String mainNavigation = '/main';
  static const String example = '/example';
  static const String noInternet = '/no-internet';

  // Movie related routes
  static const String moviePlayer = '/movie-player';
  static const String movieDetails = '/movie-details';
  static const String search = '/search';

  // Add more routes as needed
  static const String profile = '/profile';
  static const String settings = '/settings';
  static const String about = '/about';
  static const String termsOfService = '/terms-of-service';
  static const String privacyPolicy = '/privacy-policy';
  static const String contactSupport = '/contact-support';
}
